use glommio::{LocalExecutorBuilder, Latency, task};
use std::time::{Instant, Duration};

const NUM_TASKS: usize = 1_000_000; // Number of tasks to spawn
const ITERATIONS_PER_TASK: usize = 10;   // How many times each task yields

async fn ping_task(id: usize, iterations: usize) {
    for i in 0..iterations {
        // The core of the "ping" - yield control
        std::hint::black_box(id + i);
        glommio::yield_if_needed(Latency::Eventually).await;
        // Or for an even more minimal yield:
        // task::yield_now().await; (if available and suitable)

        // Potentially a tiny bit of work to prevent over-optimization
        
    }
}

fn main() {
    let exec = LocalExecutorBuilder::default()
        .name("ping-test")
        .spawn(|| async move {
            let start_time = Instant::now();

            let mut join_handles = Vec::with_capacity(NUM_TASKS);
            for i in 0..NUM_TASKS {
                join_handles.push(task::spawn_local(ping_task(i, ITERATIONS_PER_TASK)));
            }

            println!("Spawned {} tasks.", NUM_TASKS);

            for handle in join_handles {
                if let Err(e) = handle.await {
                    eprintln!("Task panicked: {:?}", e);
                }
            }

            let duration = start_time.elapsed();
            let total_yields = (NUM_TASKS * ITERATIONS_PER_TASK) as u64;
            let yields_per_second = total_yields as f64 / duration.as_secs_f64();

            println!(
                "Finished {} tasks with {} yields each in {:?}.",
                NUM_TASKS, ITERATIONS_PER_TASK, duration
            );
            println!("Total yields: {}", total_yields);
            println!("Yields per second: {:.2}", yields_per_second);
        })
        .unwrap();

    exec.join().unwrap();
}