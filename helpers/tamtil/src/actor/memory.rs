pub enum Memory {
    LongTerm(LongTermMemory),
    ShortTerm(ShortTermMemory),
}

pub struct LongTermMemory {
    pub raw: Vec<RawMemory>,
    pub visual: Vec<VisualMemory>,
}

pub struct ShortTermMemory {
    pub raw: Vec<RawMemory>,
    pub visual: Vec<VisualMemory>,
}

pub struct RawMemory {
    pub id: String,
    pub data: Vec<u8>,
}

pub struct VisualMemory {
    pub id: String,
    pub data: Vec<u8>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn long_term_memory() {
        let memory = Memory::LongTerm(LongTermMemory {
            raw: vec![RawMemory {
                id: "test".to_string(),
                data: vec![1, 2, 3],
            }],
            visual: vec![],
        });

        match memory {
            Memory::LongTerm(long_term) => {
                assert_eq!(long_term.raw.len(), 1);
            }
            Memory::ShortTerm(_) => panic!("Expected long term memory"),
        }
    }

    #[test]
    fn short_term_memory() {
        let memory = Memory::ShortTerm(ShortTermMemory {
            raw: vec![RawMemory {
                id: "test".to_string(),
                data: vec![1, 2, 3],
            }],
            visual: vec![],
        });

        match memory {
            Memory::LongTerm(_) => panic!("Expected short term memory"),
            Memory::ShortTerm(short_term) => {
                assert_eq!(short_term.raw.len(), 1);
            }
        }
    }
}
