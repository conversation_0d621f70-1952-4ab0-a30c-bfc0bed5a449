use super::alarm::*;
use super::memories::*;
use super::memory::*;

pub enum Reaction {
    Immediate(ImmediateReaction),
    Scheduled(ScheduledReaction),
}

pub struct ImmediateReaction {
    name: String,
    description: String,
    effect: fn() -> Result<(), String>,
}

impl ImmediateReaction {
    pub fn new(name: &str) -> ImmediateReactionDefinition {
        ImmediateReactionDefinition::new(name)
    }

    pub fn remember(&self, memories: &Memories) {
        // TODO: Implement immediate reaction memory
    }
}

pub struct ImmediateReactionDefinition {
    name: String,
    description: String,
    effect: fn() -> Result<(), String>,
}

impl ImmediateReactionDefinition {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            description: String::new(),
            effect: || Ok(()),
        }
    }

    pub fn description(mut self, description: &str) -> Self {
        self.description = description.to_string();
        self
    }

    pub fn effect(mut self, effect: fn() -> Result<(), String>) -> Self {
        self.effect = effect;
        self
    }

    pub fn define(self) -> ImmediateReaction {
        ImmediateReaction {
            name: self.name,
            description: self.description,
            effect: self.effect,
        }
    }
}

pub struct ScheduledReaction {
    name: String,
    description: String,
    effect: fn() -> Result<(), String>,
    schedule: Schedule,
}

impl ScheduledReaction {
    pub fn new(name: &str) -> ScheduledReactionDefinition {
        ScheduledReactionDefinition::new(name)
    }

    pub fn remember(&self, memories: &Memories) {
        // TODO: Implement scheduled reaction memory
    }
}

pub struct ScheduledReactionDefinition {
    name: String,
    description: String,
    effect: fn() -> Result<(), String>,
    schedule: Schedule,
}

impl ScheduledReactionDefinition {
    pub fn new(name: &str) -> Self {
        Self {
            name: name.to_string(),
            description: String::new(),
            effect: || Ok(()),
            schedule: Schedule::new(),
        }
    }

    pub fn description(mut self, description: &str) -> Self {
        self.description = description.to_string();
        self
    }

    pub fn effect(mut self, effect: fn() -> Result<(), String>) -> Self {
        self.effect = effect;
        self
    }

    pub fn schedule(mut self, schedule: Schedule) -> Self {
        self.schedule = schedule;
        self
    }

    pub fn define(self) -> ScheduledReaction {
        ScheduledReaction {
            name: self.name,
            description: self.description,
            effect: self.effect,
            schedule: self.schedule,
        }
    }
}
