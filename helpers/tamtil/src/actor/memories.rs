use super::memory::*;
use super::reaction::*;

pub struct Memories {
    long_term: LongTermMemory,
    short_term: ShortTermMemory,
}

impl Memories {
    pub fn new() -> Self {
        Self {
            long_term: LongTermMemory {
                raw: vec![],
                visual: vec![],
            },
            short_term: ShortTermMemory {
                raw: vec![],
                visual: vec![],
            },
        }
    }

    pub fn remember(&self, reaction: Reaction) {
        match reaction {
            Reaction::Immediate(immediate) => {
                // TODO: Implement immediate reaction memory
                immediate.remember(&self)
            }
            Reaction::Scheduled(scheduled) => {
                // TODO: Implement scheduled reaction memory
                scheduled.remember(&self)
            }
        }
    }

    pub fn recall(&self, id: &str) -> Option<&RawMemory> {
        self.long_term.raw.iter().find(|m| m.id == id)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn new_memories() {
        let memories = Memories::new();
        assert_eq!(memories.long_term.raw.len(), 0);
        assert_eq!(memories.long_term.visual.len(), 0);
        assert_eq!(memories.short_term.raw.len(), 0);
        assert_eq!(memories.short_term.visual.len(), 0);
    }
}
