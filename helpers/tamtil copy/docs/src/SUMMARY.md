# Summary

[Introduction](./introduction.md)

---

# Tutorials (Learning-Oriented)

- [Getting Started](./tutorials/new_getting-started.md)
- [Building Your First Actor](./tutorials/new_building-first-actor.md)
- [Working with Memories](./tutorials/new_working-with-memories.md)
- [Working with Visuals](./tutorials/new_working-with-visuals.md)

---

# How-To Guides (Problem-Oriented)

- [Platform Setup](./how-to/new_platform-setup.md)
- [Context Setup](./how-to/new_context-setup.md)

---

# Reference (Information-Oriented)

- [Platform API](./reference/new_platform-api.md)
- [Actor API](./reference/new_actor-api.md)
- [Memory API](./reference/new_memory-api.md)
- [Visual API](./reference/new_visual-api.md)
- [Visual Builder API](./reference/new_visual-builder-api.md)
- [Visual Behavior Builder](./reference/new_visual-behavior-builder.md)
- [Common Types](./reference/new_common-types.md)

---

# Explanation (Understanding-Oriented)

- [Actor Interaction Patterns](./explanation/new_two-interaction-patterns.md)
