# Visual Behavior Builder Reference

Complete reference for TAMTIL's client-side behavior builder. Behaviors are defined using one-word methods that automatically generate htmx attributes for actor interactions and _hyperscript code for client-side effects.

## Core Integration Pattern

TAMTIL intelligently uses both htmx and _hyperscript transparently:

- **htmx**: Handles actor actions (POST) and views (GET) using TAMTIL's URL-based addressing
- **_hyperscript**: Handles client-side visual effects using Tailwind classes

```rust
impl Reaction for InteractiveButtonStarted {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            MemoryOperation::StoreVisual {
                visual: "save-button".to_string(),
                definition: VisualDefinition::builder()
                    .tag(HtmlTag::Button)
                    .text("Save User")
                    .id("save-btn")
                    
                    // htmx: User clicks → sends POST to actor → response updates target
                    .click(Click::Post("myapp.com/users/123", Target::Selector("#user-profile")))
                    
                    // _hyperscript: Client-side visual feedback
                    .hover(Hover::Scale(Scale::OneAndFive))
                    .hover(Hover::Background(Color::Primary))
                    .leave(Leave::Scale(Scale::Normal))
                    .leave(Leave::Background(Color::Secondary))
                    
                    .build(),
                metadata: OperationMetadata::default(),
            }
        ]
    }
}

// Generates HTML with both htmx and _hyperscript:
// <button id="save-btn"
//   hx-post="myapp.com/users/123"
//   hx-target="#user-profile"
//   _="
//     on mouseenter add .scale-105 then add .bg-blue-600
//     on mouseleave remove .scale-105 then add .bg-blue-500
//   ">Save User</button>
```

## How htmx Sends Data to Actors

htmx automatically collects data from HTML elements and sends it to actors:

### Form Data (Automatic)
```rust
// htmx automatically serializes all form inputs
.submit(Submit::Post("myapp.com/users", Target::Selector("#result")))

// Generates: <form hx-post="myapp.com/users" hx-target="#result">
//   <input name="name" value="John">     ← htmx includes this
//   <input name="email" value="john@..."> ← htmx includes this
//   <button type="submit">Create</button>
// </form>
```

### Input Values (Automatic)
```rust
// htmx automatically includes the input's value
.input(Input::Post("myapp.com/search", Target::Selector("#results")))

// Generates: <input hx-post="myapp.com/search" hx-target="#results" hx-trigger="input">
// When user types "hello", htmx sends: {"value": "hello"}
```

### Button Context (Automatic)
```rust
// htmx includes data from the button's form context
.click(Click::Post("myapp.com/users/123", Target::Selector("#profile")))

// If button is inside a form, htmx includes form data
// If button has data attributes, htmx includes those
```

### TAMTIL Actor Processing
On the server side, TAMTIL actors receive the HTTP request and can:
1. Parse form data into Rust structs
2. Determine which action to take based on URL path or data
3. Return visual responses that htmx swaps into the target

## Actor Interaction Methods (htmx)

### Actor Actions (POST)
User interactions that send POST requests to actors:

```rust
// Button clicks send POST to actors (htmx gets data from form or attributes)
.click(Click::Post("myapp.com/users/123", Target::Selector("#user-profile")))                     // Click → POST to user actor → Show response in profile
.click(Click::Post("myapp.com/orders/456", Target::Me))                                           // Click → POST to order actor → Replace button

// Form submissions send POST to actors (htmx automatically serializes form data)
.submit(Submit::Post("myapp.com/users", Target::Selector("#user-list")))                          // Submit → POST form data to users actor → Add to list
.submit(Submit::Post("myapp.com/orders", Target::Selector("#orders")))                            // Submit → POST form data to orders actor → Add to orders

// Input changes send POST to actors (htmx includes input value)
.change(Change::Post("myapp.com/settings", Target::Selector("#status")))                          // Change → POST input value to settings actor → Show status
.input(Input::Post("myapp.com/search", Target::Selector("#results")))                             // Type → POST search value to search actor → Show results
```

### Actor Views (GET)
User interactions that load actor visuals:

```rust
// Navigation loads actor views
.click(Click::View("myapp.com/dashboard", Target::Selector("#main-content")))                     // Click → Load dashboard → Show in main
.click(Click::View("myapp.com/users/123", Target::Selector("#user-detail")))                      // Click → Load user → Show in detail

// Page loads get initial content
.load(Load::View("myapp.com/dashboard", Target::Selector("#content")))                            // Page load → Get dashboard → Show in content

// Polling for live updates
.poll(Poll::View("myapp.com/status", Target::Selector("#status"), Duration::Seconds(5)))          // Every 5s → Get status → Update status
```

### Response Targeting
Control where actor responses appear:

```rust
Target::Selector("#result")        // Response goes into #result element
Target::Selector("#user-list")     // Response goes into #user-list element  
Target::Me                         // Response replaces this element
Target::Parent()                   // Response goes into parent element
Target::Next(".sibling")           // Response goes into next .sibling element
```

## Client-Side Effect Methods (_hyperscript)

### Mouse Events
Visual feedback for user interactions:

```rust
// Hover effects
.hover(Hover::Scale(Scale::OneAndFive))                     // on mouseenter add .scale-105
.hover(Hover::Background(Color::Primary))                   // on mouseenter add .bg-primary
.hover(Hover::Shadow(Shadow::Large))                        // on mouseenter add .shadow-lg

.leave(Leave::Scale(Scale::Normal))                         // on mouseleave remove .scale-105
.leave(Leave::Background(Color::Secondary))                 // on mouseleave add .bg-secondary
.leave(Leave::Shadow(Shadow::None))                         // on mouseleave remove .shadow-lg

// Click feedback
.mousedown(MouseDown::Scale(Scale::ThreeQuarter))           // on mousedown add .scale-95
.mouseup(MouseUp::Scale(Scale::Normal))                     // on mouseup remove .scale-95
```

### Focus Events
Accessibility and form interactions:

```rust
.focus(Focus::Ring(Ring::Default))                          // on focus add .ring-2 .ring-blue-500
.focus(Focus::Border(Color::Primary))                       // on focus add .border-primary
.focus(Focus::Outline(Outline::None))                       // on focus add .outline-none

.blur(Blur::Ring(Ring::None))                               // on blur remove .ring-2 .ring-blue-500
.blur(Blur::Border(Color::Gray(GrayShade::Light)))          // on blur add .border-gray-300
```

### Form Events
Real-time validation and feedback:

```rust
.input(Input::If(
    Condition::Valid(),
    Action::Border(Color::Success),
    Action::Border(Color::Error)
))  // on input if valid add .border-green-500 else add .border-red-500

.change(Change::Toggle(Target::Background(Color::Primary))) // on change toggle .bg-primary
```

### Keyboard Events
Keyboard navigation and shortcuts:

```rust
.keydown(KeyDown::Key("Enter"), Action::Click(Target::Me))                      // on keydown[key=='Enter'] click me
.keydown(KeyDown::Key("Escape"), Action::Opacity(Opacity::Zero))               // on keydown[key=='Escape'] add .opacity-0
.keydown(KeyDown::Ctrl("s"), Action::Act("myapp.com/document", SaveAction))    // on keydown[ctrlKey and key=='s'] save document
```

### Conditional Behaviors
Smart visual responses:

```rust
.click(Click::If(
    Condition::Background(Color::Primary),
    Action::Background(Color::Secondary),
    Action::Background(Color::Primary)
))  // on click if .bg-primary then add .bg-secondary else add .bg-primary

.hover(Hover::Unless(
    Condition::Opacity(Opacity::Half),
    Action::Scale(Scale::OneAndFive)
))  // on mouseenter unless .opacity-50 add .scale-105
```

### Animations and Transitions
Smooth visual effects:

```rust
.click(Click::Transition(Transition::Opacity(Opacity::Zero), Duration::Milliseconds(300)))       // Fade out over 300ms
.hover(Hover::Transition(Transition::Scale(Scale::OneAndTen), Duration::Fast))                   // Scale up quickly
.focus(Focus::Transition(Transition::Ring(Ring::Default), Duration::Normal))                     // Ring appears smoothly
```

## Complete Example: User Management Interface

```rust
impl Reaction for UserInterfaceStarted {
    fn remember(&self) -> Vec<MemoryOperation> {
        vec![
            // User card with actions
            MemoryOperation::StoreVisual {
                visual: "user-card".to_string(),
                definition: VisualDefinition::builder()
                    .tag(HtmlTag::Div)
                    .class("user-card")
                    .id("user-123")
                    
                    // _hyperscript: Hover effects
                    .hover(Hover::Shadow(Shadow::Large))
                    .hover(Hover::Scale(Scale::OneAndTwo))
                    .leave(Leave::Shadow(Shadow::Medium))
                    .leave(Leave::Scale(Scale::Normal))
                    
                    .children([
                        // Edit button
                        VisualDefinition::builder()
                            .tag(HtmlTag::Button)
                            .text("Edit")
                            .class("edit-btn")
                            
                            // htmx: Click sends action to user actor, loads edit form
                            .click(Click::View("myapp.com/users/123/edit", Target::Selector("#edit-modal")))
                            
                            // _hyperscript: Button hover effect
                            .hover(Hover::Background(Color::Primary))
                            .leave(Leave::Background(Color::Secondary))
                            
                            .build(),
                        
                        // Delete button  
                        VisualDefinition::builder()
                            .tag(HtmlTag::Button)
                            .text("Delete")
                            .class("delete-btn")
                            
                            // htmx: Click sends delete action, removes card
                            .click(Click::Act("myapp.com/users/123", UserAction::Delete, Target::Me))
                            
                            // _hyperscript: Danger styling
                            .hover(Hover::Background(Color::Error))
                            .leave(Leave::Background(Color::Gray(GrayShade::Medium)))
                            
                            .build()
                    ])
                    .build(),
                metadata: OperationMetadata::default(),
            },
            
            // Search input
            MemoryOperation::StoreVisual {
                visual: "user-search".to_string(),
                definition: VisualDefinition::builder()
                    .tag(HtmlTag::Input)
                    .attr("type", "search")
                    .attr("placeholder", "Search users...")
                    .id("search-input")
                    
                    // htmx: Input sends search action, updates results
                    .input(Input::Act("myapp.com/search", SearchAction::Users, 
                        Target::Selector("#user-results"),
                        Debounce::Milliseconds(300)
                    ))
                    
                    // _hyperscript: Focus styling
                    .focus(Focus::Ring(Ring::Default))
                    .focus(Focus::Border(Color::Primary))
                    .blur(Blur::Ring(Ring::None))
                    .blur(Blur::Border(Color::Gray(GrayShade::Light)))
                    
                    .build(),
                metadata: OperationMetadata::default(),
            }
        ]
    }
}

// Generates optimized HTML:
// <div class="user-card" id="user-123" _="on mouseenter add .shadow-lg .scale-102 on mouseleave add .shadow-md remove .scale-102">
//   <button class="edit-btn" hx-get="myapp.com/users/123/edit" hx-target="#edit-modal" _="on mouseenter add .bg-primary on mouseleave add .bg-secondary">Edit</button>
//   <button class="delete-btn" hx-post="myapp.com/users/123" hx-vals='{"action":"Delete"}' hx-target="this" _="on mouseenter add .bg-red-500 on mouseleave add .bg-gray-500">Delete</button>
// </div>
// <input type="search" id="search-input" hx-post="myapp.com/search" hx-vals='{"action":"Users"}' hx-target="#user-results" hx-trigger="input delay:300ms" _="on focus add .ring-2 .border-primary on blur remove .ring-2 add .border-gray-300">
```

## Key Benefits

- **Seamless Integration**: htmx handles actor communication, _hyperscript handles visual effects
- **One-Word Methods**: Consistent `.click()`, `.hover()`, `.submit()` pattern throughout
- **Actor Pattern**: Perfect integration with TAMTIL's `actor.act(action)` and URL addressing
- **Visual Builder Integration**: Uses same Tailwind-based pattern as visual builder
- **Type Safety**: All behaviors are strongly typed with compile-time checking
- **No JavaScript**: Complete interactivity through Rust builder methods
- **Automatic Generation**: TAMTIL generates optimized htmx and _hyperscript attributes
- **Target Selection**: Easy targeting of response destinations for actor actions
- **Performance**: Minimal overhead with intelligent attribute generation
