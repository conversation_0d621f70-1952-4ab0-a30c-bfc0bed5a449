# This section is considered when running `cargo deny check licenses`
# More documentation for the licenses section can be found here:
# https://embarkstudios.github.io/cargo-deny/checks/licenses/cfg.html

[licenses]
allow = [
    "MIT",
    "Apache-2.0",
    "Apache-2.0 WITH LLVM-exception",
    "MPL-2.0",
    "Unlicense",
    "Zlib",
    "Unicode-3.0"
]
confidence-threshold = 0.95
private = { ignore = true }

[advisories]
version = 2

[bans]
multiple-versions = "allow"
