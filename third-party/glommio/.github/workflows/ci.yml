name: Rust

permissions: write-all

on:
  pull_request:
    branches: [ master ]
    types: [ "opened", "synchronize" ]
  push:
    branches: [ master ]

jobs:
  doc:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Cache setup
        uses: ./.github/actions/cache-setup

      - name: Install cargo-deadlinks
        run: which deadlinks || cargo install cargo-deadlinks

      - name: Generate documentation
        run: cargo doc --all

    # temporarily disabled.
    # - name: Validate links
    #   run: cargo deadlinks --dir target/doc/glommio

  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Cache setup
        uses: ./.github/actions/cache-setup

      - name: Build all targets
        run: cargo build --all --all-targets

  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Cache setup
        uses: ./.github/actions/cache-setup

      - name: Install cargo helpers and test all targets
        run: |
          cat << EOF > "run-gha-workflow.sh"
          PATH=$PATH:/usr/share/rust/.cargo/bin
          echo "`nproc` CPU(s) available"
          rustup install 1.70
          rustup show
          rustup default stable
          cargo install cargo-sort
          cargo test -- --test-threads=`nproc`
          EOF
          sudo -E bash -c "ulimit -Sl 512 && ulimit -Hl 512 && bash run-gha-workflow.sh"
