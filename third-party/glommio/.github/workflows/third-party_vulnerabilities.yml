name: Third-party vulnerabilities

permissions: write-all

on:
  push:
    branches:
      - master
jobs:
  check:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Run checks on third-party dependencies
        uses: EmbarkStudios/cargo-deny-action@v2
        with:
          log-level: warn
          command: check
          arguments: --all-features
