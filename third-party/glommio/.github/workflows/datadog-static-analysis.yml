on: [push]

name: Datadog Static Analysis

jobs:
  static-analysis:
    runs-on: ubuntu-latest
    name: Datadog Static Analyzer
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Check code meets quality and security standards
      id: datadog-static-analysis
      uses: DataDog/datadog-static-analyzer-github-action@v1
      with:
        dd_api_key: ${{ secrets.DD_STATIC_ANALYSIS_API_KEY }}
        dd_app_key: ${{ secrets.DD_STATIC_ANALYSIS_APP_KEY }}
        dd_site: datadoghq.com
        cpu_count: 2
