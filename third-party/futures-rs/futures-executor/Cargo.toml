[package]
name = "futures-executor"
version = "0.4.0-alpha.0"
edition = "2018"
rust-version = "1.68"
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/futures-rs"
homepage = "https://rust-lang.github.io/futures-rs"
description = """
Executors for asynchronous tasks based on the futures-rs library.
"""

[features]
default = ["std"]
std = ["futures-core/std", "futures-task/std", "futures-util/std"]
thread-pool = ["std"]

[dependencies]
futures-core = { path = "../futures-core", version = "=1.0.0-alpha.0", default-features = false }
futures-task = { path = "../futures-task", version = "=0.4.0-alpha.0", default-features = false }
futures-util = { path = "../futures-util", version = "=0.4.0-alpha.0", default-features = false }

[dev-dependencies]
futures = { path = "../futures", features = ["thread-pool"] }

[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

[lints]
workspace = true
