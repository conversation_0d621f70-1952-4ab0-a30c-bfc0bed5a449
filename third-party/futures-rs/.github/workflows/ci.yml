name: CI

permissions:
  contents: read

on:
  pull_request:
  push:
    branches:
      - master
      - '[0-9]+.[0-9]+'
  schedule:
    - cron: '0 2 * * *'

env:
  CARGO_INCREMENTAL: 0
  CARGO_NET_GIT_FETCH_WITH_CLI: true
  CARGO_NET_RETRY: 10
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: 1
  RUSTDOCFLAGS: -D warnings
  RUSTFLAGS: -D warnings
  RUSTUP_MAX_RETRIES: 10

defaults:
  run:
    shell: bash

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.sha }}
  cancel-in-progress: true

jobs:
  test:
    name: cargo test
    strategy:
      fail-fast: false
      matrix:
        include:
          - os: ubuntu-latest
          - os: macos-latest
          - os: windows-latest
          - os: ubuntu-24.04-arm
          - os: ubuntu-24.04-arm
            target: armv7-unknown-linux-gnueabihf
          - os: ubuntu-latest
            target: armv5te-unknown-linux-gnueabi
          - os: ubuntu-latest
            target: i686-unknown-linux-gnu
    runs-on: ${{ matrix.os }}
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
      - uses: taiki-e/setup-cross-toolchain-action@v1
        with:
          target: ${{ matrix.target }}
        if: matrix.target != ''
      - run: cargo test --workspace --all-features $DOCTEST_XCOMPILE
      - run: cargo test --workspace --all-features --release $DOCTEST_XCOMPILE

  core-msrv:
    name: cargo +${{ matrix.rust }} build (futures-{core, io, sink})
    strategy:
      matrix:
        rust:
          # This is the minimum Rust version supported by futures-core, futures-io, futures-sink.
          # When updating this, the reminder to update the minimum required version in README.md and Cargo.toml.
          - '1.36'
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@main
        with:
          toolchain: ${{ matrix.rust }}
      # cargo does not support for --features/--no-default-features with workspace, so use cargo-hack instead.
      # Refs: cargo#3620, cargo#4106, cargo#4463, cargo#4753, cargo#5015, cargo#5364, cargo#6195
      - name: Install cargo-hack
        uses: taiki-e/install-action@cargo-hack
      # remove dev-dependencies to avoid https://github.com/rust-lang/cargo/issues/4866
      - run: cargo hack --remove-dev-deps --workspace
      # Check no-default-features
      - run: |
          cargo hack build --workspace --ignore-private --no-default-features \
            --exclude futures --exclude futures-util --exclude futures-task --exclude futures-macro --exclude futures-executor --exclude futures-channel --exclude futures-test
      # Check alloc feature
      - run: |
          cargo hack build --workspace --ignore-private --no-default-features --features alloc --ignore-unknown-features \
            --exclude futures --exclude futures-util --exclude futures-task --exclude futures-macro --exclude futures-executor --exclude futures-channel --exclude futures-test
      # Check std feature
      - run: |
          cargo hack build --workspace --ignore-private --no-default-features --features std \
            --exclude futures --exclude futures-util --exclude futures-task --exclude futures-macro --exclude futures-executor --exclude futures-channel --exclude futures-test

  util-msrv:
    name: cargo +${{ matrix.rust }} build
    strategy:
      matrix:
        rust:
          # This is the minimum Rust version supported by futures, futures-util, futures-task, futures-macro, futures-executor, futures-channel, futures-test.
          # When updating this, the reminder to update the minimum required version in README.md and Cargo.toml.
          - '1.68'
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@main
        with:
          toolchain: ${{ matrix.rust }}
      - name: Install cargo-hack
        uses: taiki-e/install-action@cargo-hack
      # remove dev-dependencies to avoid https://github.com/rust-lang/cargo/issues/4866
      - run: cargo hack --remove-dev-deps --workspace
      # Check default features
      - run: cargo hack build --workspace --ignore-private
      # Check no-default-features
      - run: cargo hack build --workspace --exclude futures-test --ignore-private --no-default-features
      # Check alloc feature
      - run: cargo hack build --workspace --exclude futures-test --ignore-private --no-default-features --features alloc --ignore-unknown-features
      # Check std feature
      - run: cargo hack build --workspace --ignore-private --no-default-features --features std --ignore-unknown-features
      # Check compat feature (futures, futures-util)
      # Exclude io-compat feature because the MSRV when it is enabled depends on the MSRV of tokio 0.1.
      - run: cargo hack build -p futures -p futures-util --no-default-features --features std,compat
      # Check thread-pool feature (futures, futures-executor)
      - run: cargo hack build -p futures -p futures-executor --no-default-features --features std,thread-pool

  build:
    name: cargo +${{ matrix.rust }} build
    strategy:
      fail-fast: false
      matrix:
        rust:
          - stable
          - beta
          - nightly
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@main
        with:
          toolchain: ${{ matrix.rust }}
      - name: Install cargo-hack
        uses: taiki-e/install-action@cargo-hack
      - run: cargo hack build --workspace --no-dev-deps
      - run: cargo build --tests --features default,thread-pool,io-compat --manifest-path futures/Cargo.toml

  minimal-versions:
    name: cargo minimal-versions build
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
      - name: Install cargo-hack
        uses: taiki-e/install-action@cargo-hack
      - name: Install cargo-minimal-versions
        uses: taiki-e/install-action@cargo-minimal-versions
      - run: cargo minimal-versions build --workspace --ignore-private --all-features

  no-std:
    name: cargo build --target ${{ matrix.target }}
    strategy:
      fail-fast: false
      matrix:
        # thumbv7m-none-eabi supports atomic CAS.
        # thumbv6m-none-eabi supports atomic, but not atomic CAS.
        target:
          - thumbv7m-none-eabi
          - thumbv6m-none-eabi
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
        with:
          target: ${{ matrix.target }}
      - name: Install cargo-hack
        uses: taiki-e/install-action@cargo-hack
      # remove dev-dependencies to avoid https://github.com/rust-lang/cargo/issues/4866
      - run: cargo hack --remove-dev-deps --workspace
      - run: |
          cargo hack build --manifest-path futures/tests/no-std/Cargo.toml \
            --each-feature --optional-deps \
            --target ${{ matrix.target }}
      - run: |
          cargo hack build --workspace --ignore-private \
            --exclude futures-test --exclude futures-macro \
            --no-default-features \
            --target ${{ matrix.target }}
      - run: |
          cargo hack build --workspace --ignore-private \
            --exclude futures-test --exclude futures-macro \
            --no-default-features --features alloc --ignore-unknown-features \
            --target ${{ matrix.target }}
      - run: |
          cargo hack build --workspace --ignore-private \
            --exclude futures-test --exclude futures-macro \
            --no-default-features --features async-await,alloc --ignore-unknown-features \
            --target ${{ matrix.target }}

  bench:
    name: cargo bench
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
      - run: cargo bench --workspace
      - run: cargo bench --manifest-path futures-util/Cargo.toml --features=bilock,unstable

  features:
    name: cargo hack check --feature-powerset
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
      - name: Install cargo-hack
        uses: taiki-e/install-action@cargo-hack
      # Check each specified feature works properly
      # * `--feature-powerset` - run for the feature powerset of the package
      # * `--depth 2` - limit the max number of simultaneous feature flags of `--feature-powerset`
      # * `--no-dev-deps` - build without dev-dependencies to avoid https://github.com/rust-lang/cargo/issues/4866
      # * `--exclude futures-test` - futures-test cannot be compiled with no-default features
      # * `--features unstable` - some features cannot be compiled without this feature
      # * `--ignore-unknown-features` - some crates doesn't have 'unstable' feature
      - run: |
          cargo hack check \
            --feature-powerset --depth 2 --no-dev-deps \
            --workspace --exclude futures-test \
            --features unstable --ignore-unknown-features

  miri:
    name: cargo miri test
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
        with:
          component: miri
      - run: cargo miri test --workspace --all-features -- --skip panic_on_drop_fut
        env:
          MIRIFLAGS: -Zmiri-strict-provenance -Zmiri-symbolic-alignment-check -Zmiri-disable-isolation
          RUSTDOCFLAGS: ${{ env.RUSTDOCFLAGS }} -Z randomize-layout
          RUSTFLAGS: ${{ env.RUSTFLAGS }} -Z randomize-layout
      # This test is expected to leak.
      - run: cargo miri test --workspace --all-features --test stream_futures_unordered -- panic_on_drop_fut
        env:
          MIRIFLAGS: -Zmiri-strict-provenance -Zmiri-symbolic-alignment-check -Zmiri-disable-isolation -Zmiri-ignore-leaks
          RUSTDOCFLAGS: ${{ env.RUSTDOCFLAGS }} -Z randomize-layout
          RUSTFLAGS: ${{ env.RUSTFLAGS }} -Z randomize-layout

  san:
    name: cargo test -Z sanitizer=${{ matrix.sanitizer }}
    strategy:
      fail-fast: false
      matrix:
        sanitizer:
          - address
          - memory
          - thread
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
        with:
          component: rust-src
      # https://github.com/google/sanitizers/issues/1716 / https://github.com/actions/runner-images/issues/9491
      - run: sudo sysctl vm.mmap_rnd_bits=28
      # Exclude futures-macro to work around upstream bug since nightly-2024-10-06.
      - run: cargo -Z build-std test --workspace --all-features --target x86_64-unknown-linux-gnu --lib --tests --exclude futures-macro -- --skip panic_on_drop_fut
        env:
          # TODO: Once `cfg(sanitize = "..")` is stable, replace
          # `cfg(futures_sanitizer)` with `cfg(sanitize = "..")` and remove
          # `--cfg futures_sanitizer`.
          RUSTFLAGS: ${{ env.RUSTFLAGS }} -Z sanitizer=${{ matrix.sanitizer }} --cfg futures_sanitizer

  clippy:
    name: cargo clippy
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@stable
        with:
          component: clippy
      - run: cargo clippy --workspace --all-features --lib --bins --tests --examples

  fmt:
    name: cargo fmt
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@stable
        with:
          component: rustfmt
      - run: cargo fmt --all -- --check

  docs:
    name: cargo doc
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - uses: taiki-e/checkout-action@v1
      - name: Install Rust
        uses: taiki-e/github-actions/install-rust@nightly
      - run: cargo doc --workspace --no-deps --all-features
        env:
          RUSTDOCFLAGS: ${{ env.RUSTDOCFLAGS }} --cfg docsrs
