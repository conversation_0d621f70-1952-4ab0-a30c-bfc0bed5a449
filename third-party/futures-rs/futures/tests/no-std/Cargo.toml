[package]
name = "no-std"
version = "0.0.0"
edition = "2018"
publish = false

[features]
futures-core-alloc = ["futures-core/alloc"]
futures-task-alloc = ["futures-task/alloc"]
futures-channel-alloc = ["futures-channel/alloc"]
futures-util-alloc = ["futures-util/alloc"]
futures-util-async-await = ["futures-util/async-await"]
futures-alloc = ["futures/alloc"]
futures-async-await = ["futures/async-await"]

[dependencies]
futures-core = { path = "../../../futures-core", optional = true, default-features = false }
futures-task = { path = "../../../futures-task", optional = true, default-features = false }
futures-channel = { path = "../../../futures-channel", optional = true, default-features = false }
futures-util = { path = "../../../futures-util", optional = true, default-features = false }
futures = { path = "../..", optional = true, default-features = false }

[lints]
workspace = true
