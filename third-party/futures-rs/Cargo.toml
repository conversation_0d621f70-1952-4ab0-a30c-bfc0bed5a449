[workspace]
members = [
  "futures",
  "futures-core",
  "futures-channel",
  "futures-executor",
  "futures-io",
  "futures-macro",
  "futures-sink",
  "futures-task",
  "futures-util",
  "futures-test",

  "futures/tests/macro-tests",
  "futures/tests/macro-reexport",
  "futures/tests/no-std",

  "examples/functional",
  "examples/imperative",
]

[workspace.lints.rust]
missing_debug_implementations = "warn"
rust_2018_idioms = "warn"
single_use_lifetimes = "warn"
unexpected_cfgs = { level = "warn", check-cfg = [
  'cfg(futures_sanitizer)',
] }
unreachable_pub = "warn"
# unsafe_op_in_unsafe_fn = "warn" # Set at crate-level instead since https://github.com/rust-lang/rust/pull/100081 is not available on MSRV
[workspace.lints.clippy]
incompatible_msrv = { level = "allow", priority = 1 } # https://github.com/rust-lang/rust-clippy/issues/12273, https://github.com/rust-lang/rust-clippy/issues/12257
