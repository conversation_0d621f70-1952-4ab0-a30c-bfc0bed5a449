# futures-sink

The asynchronous `Sink` trait for the futures-rs library.

## Usage

Add this to your `Cargo.toml`:

```toml
[dependencies]
futures-sink = "0.3"
```

The current `futures-sink` requires Rust 1.36 or later.

## License

Licensed under either of [Apache License, Version 2.0](LICENSE-APACHE) or
[MIT license](LICENSE-MIT) at your option.

Unless you explicitly state otherwise, any contribution intentionally submitted
for inclusion in the work by you, as defined in the Apache-2.0 license, shall
be dual licensed as above, without any additional terms or conditions.
