[package]
name = "futures-sink"
version = "0.4.0-alpha.0"
edition = "2018"
rust-version = "1.36"
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/futures-rs"
homepage = "https://rust-lang.github.io/futures-rs"
description = """
The asynchronous `Sink` trait for the futures-rs library.
"""

[features]
default = ["std"]
std = ["alloc"]
alloc = []

[dependencies]

[package.metadata.docs.rs]
all-features = true

[lints]
workspace = true
