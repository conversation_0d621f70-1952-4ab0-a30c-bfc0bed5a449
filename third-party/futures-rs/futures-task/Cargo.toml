[package]
name = "futures-task"
version = "0.4.0-alpha.0"
edition = "2018"
rust-version = "1.68"
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/futures-rs"
homepage = "https://rust-lang.github.io/futures-rs"
description = """
Tools for working with tasks.
"""

[features]
default = ["std"]
std = ["alloc"]
alloc = []

[dependencies]

[dev-dependencies]
futures = { path = "../futures" }

[package.metadata.docs.rs]
all-features = true

[lints]
workspace = true
