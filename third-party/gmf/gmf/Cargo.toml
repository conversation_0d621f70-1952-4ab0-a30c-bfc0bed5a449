[package]
name = "gmf"
version = "1.0.0"
edition = "2021"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
description = "An RPC library using Glommio and Tonic. Note: This package only works on Linux."
license = "MIT"
readme = "../README.md"
repository = "https://github.com/EtaCassiopeia/gmf"
keywords = ["protobuf", "grpc", "futures", "async", "glommio"]
categories = ["asynchronous","network-programming","web-programming"]

[build]
target = ["x86_64-unknown-linux-gnu", "i686-unknown-linux-gnu","aarch64-unknown-linux-gnu"]

target_os = "linux"

[dependencies]
tonic = { version = "0.9.2", features = ["transport"] }
glommio = "0.8.0"
async-io = "1.13.0"
async-trait = "0.1.57"
serde = { version = "1.0.144", features = ["derive"] }
log = "0.4.17"
env_logger = "0.10.0"
futures = { version = "0.3", default-features = false }
futures-core = "0.3.25"
futures-util = "0.3.25"
futures-lite = "1.12.0"
futures-time = "3.0.0"
async-stream = "0.3.3"
tower-service = "0.3.2"
hyper = { version = "0.14.20", features = ["server", "http1", "http2"] }
tokio = "1.21.2"
warp = { version = "0.3.4", default-features = false }
ctrlc-async = "3.2.2"
tower =  "0.4"
http =  "0.2"
http-body = { version = "0.4.2" }
pin-project = "1.1.0"
num_cpus = "1.13.0"