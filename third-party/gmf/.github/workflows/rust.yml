name: Rust CI

on:
  push:
    branches:
      - master
      - release
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install Rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true

      - name: Install Protobuf Compiler
        run: |
          sudo apt-get update
          sudo apt-get install -y protobuf-compiler          

      - name: Build
        uses: actions-rs/cargo@v1
        with:
          command: build
          args: --workspace --all-targets

      - name: Run tests
        uses: actions-rs/cargo@v1
        with:
          command: test
          args: --workspace

  release:

    needs: build
    if: github.ref == 'refs/heads/release' || github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install Rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: stable
          override: true

      - name: Install Protobuf Compiler
        run: |
          sudo apt-get update
          sudo apt-get install -y protobuf-compiler          

      - name: Login to crates.io
        uses: actions-rs/cargo@v1
        with:
          command: login
          args: ${{ secrets.CRATES_IO_TOKEN }}

      - name: Get version
        id: vars
        run: echo tag=$(grep "^version" ./gmf/Cargo.toml | head -1 | awk -F'"' '{ print $2 }') >> $GITHUB_OUTPUT

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.vars.outputs.tag }}
          release_name: Release ${{ steps.vars.outputs.tag }}
          draft: false
          prerelease: false

      - name: Publish
        uses: actions-rs/cargo@v1
        with:
          command: publish
          args: --manifest-path gmf/Cargo.toml
