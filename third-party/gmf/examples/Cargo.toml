[package]
authors = ["<PERSON><PERSON><PERSON> Zainalpour <<EMAIL>>"]
edition = "2021"
license = "MIT"
name = "examples"
publish = false
version = "0.1.0"

[[bin]]
name = "helloworld-gmf-server"
path = "src/helloworld-gmf/server.rs"
required-features = ["hyper-warp"]

[[bin]]
name = "helloworld-gmf-client"
path = "src/helloworld-gmf/client.rs"
required-features = ["hyper-warp"]


[[bin]]
name = "helloworld-tonic-server"
path = "src/helloworld-tonic/server.rs"
required-features = ["hyper-warp"]

[[bin]]
name = "helloworld-tonic-client"
path = "src/helloworld-tonic/client.rs"
required-features = ["hyper-warp"]

[[bench]]
name = "benchmark"
harness = false

[features]
hyper-warp = ["dep:futures", "dep:tower", "dep:hyper", "dep:http", "dep:http-body", "dep:warp"]

[dependencies]
tonic = { version = "0.9.2", features = ["transport"] }
glommio = "0.8.0"
async-trait = "0.1.57"
serde = { version = "1.0.144", features = ["derive"] }
log = "0.4.17"
env_logger = "0.10.0"
futures = { version = "0.3", default-features = false, optional = true }
futures-core = "0.3.25"
futures-util = "0.3.25"
async-stream = "0.3.3"
ctrlc-async = "3.2.2"
tower-service = "0.3.2"
async-io = "1.13.0"
hyper = { version = "0.14.20", features = ["server", "http1", "http2"] ,optional=true}
tokio = { version = "1.21.2", features = ["full", "rt-multi-thread"] }
warp = { version = "0.3.4", default-features = false, optional = true }
tower = { version = "0.4", optional = true }
http = { version = "0.2", optional = true }
http-body = { version = "0.4.2", optional = true }
prost = "0.11"
gmf = { version = "1.0.0", path = "../gmf" }

[build-dependencies]
tonic-build = "0.9.2"

[dev-dependencies]
criterion = "0.4.0"