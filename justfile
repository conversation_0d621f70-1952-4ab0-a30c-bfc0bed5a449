# TAMTIL Development Commands
# Uses nerdctl with colima/containerd and latest Alpine Linux

# Default recipe - show available commands
default:
    @just --list

# Run the project
run *args:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo run {{args}}"

# Run tests
test *args:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo test {{args}}"

# Check code (clippy + fmt)
check:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo clippy --all-targets --all-features -- -D warnings && cargo fmt --all -- --check"

# Format code
fmt:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "cargo fmt --all"

# Build the project
build *args:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo build {{args}}"

# Start development environment
dev-start:
    @echo "Starting TAMTIL development environment..."
    nerdctl compose -f docker-compose.dev.yml up -d
    @echo "Development environment started!"
    @echo "Services available:"
    @echo "  - Database: localhost:5432"
    @echo "  - Redis: localhost:6379"
    @echo "  - NATS: localhost:4222"

# Stop development environment
dev-stop:
    @echo "Stopping TAMTIL development environment..."
    nerdctl compose -f docker-compose.dev.yml down
    @echo "Development environment stopped!"

# Restart development environment
dev-restart: dev-stop dev-start

# Show development environment status
dev-status:
    nerdctl compose -f docker-compose.dev.yml ps

# View development environment logs
dev-logs service="":
    #!/usr/bin/env bash
    if [ -z "{{service}}" ]; then
        nerdctl compose -f docker-compose.dev.yml logs -f
    else
        nerdctl compose -f docker-compose.dev.yml logs -f {{service}}
    fi

# Clean up development environment (removes volumes)
dev-clean:
    @echo "Cleaning up development environment..."
    nerdctl compose -f docker-compose.dev.yml down -v
    nerdctl system prune -f
    @echo "Development environment cleaned!"

# Install development dependencies
dev-setup:
    @echo "Setting up development environment..."
    @echo "Installing just..."
    @which just > /dev/null || (echo "Please install 'just' command runner: https://github.com/casey/just" && exit 1)
    @echo "Installing colima..."
    @which colima > /dev/null || (echo "Please install colima: brew install colima" && exit 1)
    @echo "Installing nerdctl..."
    @which nerdctl > /dev/null || (echo "Please install nerdctl: brew install nerdctl" && exit 1)
    @echo "Starting colima if not running..."
    @colima status > /dev/null 2>&1 || colima start
    @echo "Development environment setup complete!"

# Run cargo commands in Alpine container
cargo *args:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo {{args}}"

# Open a shell in Alpine container for debugging
shell:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev bash && bash"

# Run benchmarks
bench *args:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo bench {{args}}"

# Generate documentation
docs:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo doc --no-deps"

# Watch for changes and run tests
watch-test:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo install cargo-watch && cargo watch -x test"

# Watch for changes and run checks
watch-check:
    nerdctl run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo install cargo-watch && cargo watch -x 'clippy --all-targets --all-features'"
