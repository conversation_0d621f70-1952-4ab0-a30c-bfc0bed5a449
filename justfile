# TAMTIL Development Commands
# Uses latest Alpine Linux for consistent development environment

# Default recipe - show available commands
default:
    @just --list

# Run the project
run *args:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo run {{args}}"

# Run tests
test *args:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo test {{args}}"

# Check code (clippy + fmt)
check:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo clippy --all-targets --all-features -- -D warnings && cargo fmt --all -- --check"

# Format code
fmt:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "cargo fmt --all"

# Build the project
build *args:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo build {{args}}"

# Start development environment
dev-start:
    @echo "Starting TAMTIL development environment..."
    docker-compose -f docker-compose.dev.yml up -d
    @echo "Development environment started!"
    @echo "Services available:"
    @echo "  - Database: localhost:5432"
    @echo "  - Redis: localhost:6379"
    @echo "  - NATS: localhost:4222"

# Stop development environment
dev-stop:
    @echo "Stopping TAMTIL development environment..."
    docker-compose -f docker-compose.dev.yml down
    @echo "Development environment stopped!"

# Restart development environment
dev-restart: dev-stop dev-start

# Show development environment status
dev-status:
    docker-compose -f docker-compose.dev.yml ps

# View development environment logs
dev-logs service="":
    #!/usr/bin/env bash
    if [ -z "{{service}}" ]; then
        docker-compose -f docker-compose.dev.yml logs -f
    else
        docker-compose -f docker-compose.dev.yml logs -f {{service}}
    fi

# Clean up development environment (removes volumes)
dev-clean:
    @echo "Cleaning up development environment..."
    docker-compose -f docker-compose.dev.yml down -v
    docker system prune -f
    @echo "Development environment cleaned!"

# Install development dependencies
dev-setup:
    @echo "Setting up development environment..."
    @echo "Installing just..."
    @which just > /dev/null || (echo "Please install 'just' command runner: https://github.com/casey/just" && exit 1)
    @echo "Installing docker..."
    @which docker > /dev/null || (echo "Please install Docker: https://docs.docker.com/get-docker/" && exit 1)
    @echo "Installing docker-compose..."
    @which docker-compose > /dev/null || (echo "Please install Docker Compose: https://docs.docker.com/compose/install/" && exit 1)
    @echo "Development environment setup complete!"

# Run cargo commands in Alpine container
cargo *args:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo {{args}}"

# Open a shell in Alpine container for debugging
shell:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev bash && bash"

# Run benchmarks
bench *args:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo bench {{args}}"

# Generate documentation
docs:
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo doc --no-deps --open"

# Watch for changes and run tests
watch-test:
    @echo "Install cargo-watch first: cargo install cargo-watch"
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo install cargo-watch && cargo watch -x test"

# Watch for changes and run checks
watch-check:
    @echo "Install cargo-watch first: cargo install cargo-watch"
    docker run --rm -it \
        -v {{justfile_directory()}}:/workspace \
        -w /workspace \
        rust:alpine \
        sh -c "apk add --no-cache musl-dev && cargo install cargo-watch && cargo watch -x 'clippy --all-targets --all-features'"
